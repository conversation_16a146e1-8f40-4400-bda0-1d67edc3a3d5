module.exports = {
  apps: [
    {
      name: 'fin-mcp-server-http',
      script: './fin_mcp_server',
      cwd: '/opt/00_APPS/FINITY_AI/fin_mcp_server',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env_file: '.env',
      env: {
        NODE_ENV: 'production',
        MCP_TRANSPORT: 'streamable-http',
        MCP_PORT: 8080
      },
      log_file: './logs/fin-mcp-server.log',
      out_file: './logs/fin-mcp-server-out.log',
      error_file: './logs/fin-mcp-server-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 5000,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'fin-mcp-server-sse',
      script: './fin_mcp_server',
      cwd: '/opt/00_APPS/FINITY_AI/fin_mcp_server',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env_file: '.env',
      env: {
        NODE_ENV: 'production',
        MCP_TRANSPORT: 'sse',
        MCP_PORT: 8081
      },
      log_file: './logs/fin-mcp-server-sse.log',
      out_file: './logs/fin-mcp-server-sse-out.log',
      error_file: './logs/fin-mcp-server-sse-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      kill_timeout: 5000,
      restart_delay: 1000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
