# PostgreSQL MCP Server

A Model Context Protocol (MCP) server that provides AI assistants with secure access to PostgreSQL databases. Built with <PERSON> using the `mcp-go` library, supporting multiple transport protocols including STDIO, StreamableHTTP, and Server-Sent Events (SSE).

## Features

- 🔌 **Multiple Transport Protocols**: STDIO, StreamableHTTP, SSE
- 🗄️ **PostgreSQL Integration**: Query tables and views with built-in safety
- 🚀 **Production Ready**: PM2 process management, health checks, graceful shutdown
- 🔒 **Secure**: Parameterized queries, connection pooling
- 📊 **Monitoring**: Health check endpoints and structured logging
- ⚡ **Process Management**: PM2 support for production deployment

## Quick Start

### Prerequisites

- Go 1.23 or later
- PostgreSQL database
- PM2 (for production deployment)
- Node.js and npm (for PM2 installation)

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd fin_mcp_server
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Create environment file**:
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Install PM2** (if not already installed):
   ```bash
   npm install -g pm2
   ```

### Configuration

Create a `.env` file in the project root:

```bash
# MCP Server Configuration
MCP_TRANSPORT=streamable-http
MCP_PORT=8080

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=postgres
```

### Running the Server

#### Development Mode

**STDIO Mode** (default):
```bash
go run main.go
```

**HTTP Mode**:
```bash
./scripts/start-http.sh
# Server available at: http://localhost:8080/mcp
```

**SSE Mode**:
```bash
./scripts/start-sse.sh
# Server available at: http://localhost:8081
```

#### Production Mode with PM2

**Quick Setup**:
```bash
./scripts/setup-pm2.sh
```

**Manual Setup**:
```bash
# Build application
go build -o fin_mcp_server main.go

# Start PM2 services
pm2 start ecosystem.config.js

# Monitor services
pm2 status
pm2 logs
```

## Available Tools

### `query_table`
Query data from PostgreSQL tables.

**Parameters**:
- `table` (required): Table name to query
- `limit` (optional): Number of rows to return (default: 10)

**Example**:
```json
{
  "name": "query_table",
  "arguments": {
    "table": "users",
    "limit": "5"
  }
}
```

### `query_view`
Query data from PostgreSQL views.

**Parameters**:
- `view` (required): View name to query  
- `limit` (optional): Number of rows to return (default: 10)

**Example**:
```json
{
  "name": "query_view",
  "arguments": {
    "view": "user_orders",
    "limit": "10"
  }
}
```

## API Endpoints

When running in HTTP or SSE mode:

| Endpoint | Description |
|----------|-------------|
| `/mcp` | MCP protocol endpoint |
| `/health` | Health check endpoint |
| `/` | Server information |

### Health Check Response
```json
{
  "status": "healthy",
  "database": "connected",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## MCP Client Configuration

### STDIO Transport
```json
{
  "mcpServers": {
    "pg-mcp-stdio": {
      "command": "go",
      "args": ["run", "main.go"],
      "cwd": "/path/to/fin_mcp_server",
      "env": {
        "POSTGRES_HOST": "localhost",
        "POSTGRES_PORT": "5432",
        "POSTGRES_USER": "postgres",
        "POSTGRES_PASSWORD": "postgres",
        "POSTGRES_DB": "postgres",
        "MCP_TRANSPORT": "stdio"
      }
    }
  }
}
```

### StreamableHTTP Transport
```json
{
  "mcpServers": {
    "pg-mcp-http": {
      "transport": "streamable-http",
      "url": "http://localhost:8080/mcp"
    }
  }
}
```

## Development

### Project Structure
```
fin_mcp_server/
├── main.go              # Application entry point
├── internal/
│   ├── db/
│   │   └── postgres.go  # Database connection
│   └── mcp/
│       ├── server.go    # MCP server implementation
│       └── tools.go     # MCP tools (query_table, query_view)
├── scripts/             # Shell scripts
│   ├── start-http.sh    # HTTP server startup script
│   ├── start-sse.sh     # SSE server startup script
│   ├── setup-pm2.sh     # PM2 setup and deployment script
│   ├── start-pm2.sh     # Simple PM2 start script
│   ├── stop-pm2.sh      # PM2 stop script
│   └── load-env.sh      # Environment loader script
├── ecosystem.config.js  # PM2 configuration
├── .env                 # Environment variables
├── init.sql            # Database initialization
└── .mcp.json           # MCP client configuration
```

### Building
```bash
go build -o fin_mcp_server main.go
```

### Testing
```bash
# Test health endpoint
curl http://localhost:8080/health

# Test server info
curl http://localhost:8080/

# Test with PM2
pm2 status
pm2 logs
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MCP_TRANSPORT` | Transport protocol (`stdio`, `streamable-http`, `sse`) | `stdio` |
| `MCP_PORT` | Server port (for HTTP/SSE modes) | `8080` |
| `POSTGRES_HOST` | PostgreSQL host | `localhost` |
| `POSTGRES_PORT` | PostgreSQL port | `5432` |
| `POSTGRES_USER` | PostgreSQL username | `postgres` |
| `POSTGRES_PASSWORD` | PostgreSQL password | `postgres` |
| `POSTGRES_DB` | PostgreSQL database name | `postgres` |



## PM2 Commands

```bash
# Start services
pm2 start ecosystem.config.js

# Stop services
pm2 stop ecosystem.config.js

# Restart services
pm2 restart ecosystem.config.js

# View logs
pm2 logs

# Monitor services
pm2 monit

# Check status
pm2 status
```

## Security Considerations

- Uses parameterized queries to prevent SQL injection
- Process management with PM2 for production stability
- Database connections are pooled and managed
- Environment variables for sensitive configuration
- Health checks for monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license here]
