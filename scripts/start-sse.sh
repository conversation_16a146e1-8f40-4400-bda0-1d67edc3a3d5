#!/bin/bash

# Script untuk menjalankan MCP Server dalam mode SSE

echo "Starting PostgreSQL MCP Server in SSE mode..."

# Load environment variables from .env file
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
else
    echo "Warning: .env file not found! Using default values..."
fi

# Set specific transport for this script
export MCP_TRANSPORT=sse
export MCP_PORT=8081

echo "Configuration:"
echo "=============="
echo "  Transport: $MCP_TRANSPORT"
echo "  Port: $MCP_PORT"
echo ""
echo "Environment Variables:"
while IFS='=' read -r key value; do
    # Skip comments and empty lines
    if [[ ! "$key" =~ ^#.*$ ]] && [[ -n "$key" ]]; then
        # Remove any quotes from the value for display
        clean_value=$(echo "$value" | sed 's/^["'\'']*//;s/["'\'']*$//')
        echo "  $key: $clean_value"
    fi
done < .env
echo ""

# Build and run
go build -o fin_mcp_server main.go
if [ $? -eq 0 ]; then
    echo "Build successful. Starting server..."
    echo "Server will be available at: http://localhost:$MCP_PORT"
    echo "Press Ctrl+C to stop the server"
    echo ""
    ./fin_mcp_server
else
    echo "Build failed!"
    exit 1
fi
