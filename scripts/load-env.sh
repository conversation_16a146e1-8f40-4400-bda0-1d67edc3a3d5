#!/bin/bash

# Script untuk load environment variables dari .env file

if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "Environment variables loaded successfully!"
else
    echo "Warning: .env file not found!"
    exit 1
fi

# Display loaded configuration dynamically
echo ""
echo "Current Configuration:"
echo "======================"
while IFS='=' read -r key value; do
    # Skip comments and empty lines
    if [[ ! "$key" =~ ^#.*$ ]] && [[ -n "$key" ]]; then
        # Remove any quotes from the value for display
        clean_value=$(echo "$value" | sed 's/^["'\'']*//;s/["'\'']*$//')
        echo "  $key: $clean_value"
    fi
done < .env
echo ""
