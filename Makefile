# Makefile for PostgreSQL MCP Server

# Variables
BINARY_NAME=fin_mcp_server
GO_FILES=$(shell find . -name "*.go" -type f)

# Default target
.PHONY: help
help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Build commands
.PHONY: build
build: ## Build the binary
	go build -o $(BINARY_NAME) main.go

.PHONY: build-linux
build-linux: ## Build for Linux
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o $(BINARY_NAME) main.go

.PHONY: clean
clean: ## Clean build artifacts
	rm -f $(BINARY_NAME)
	go clean

# Development commands
.PHONY: run
run: build ## Run in STDIO mode
	./$(BINARY_NAME)

.PHONY: run-http
run-http: build ## Run in StreamableHTTP mode
	./scripts/start-http.sh

.PHONY: run-sse
run-sse: build ## Run in SSE mode
	./scripts/start-sse.sh

.PHONY: dev
dev: ## Run with hot reload (requires air)
	air

# Testing commands
.PHONY: test
test: ## Run tests
	go test -v ./...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

.PHONY: test-race
test-race: ## Run tests with race detection
	go test -race -v ./...

# Code quality commands
.PHONY: fmt
fmt: ## Format Go code
	go fmt ./...

.PHONY: vet
vet: ## Run go vet
	go vet ./...

.PHONY: lint
lint: ## Run golangci-lint (requires golangci-lint)
	golangci-lint run

.PHONY: tidy
tidy: ## Tidy go modules
	go mod tidy

# PM2 commands
.PHONY: pm2-start
pm2-start: build setup-logs ## Start services with PM2
	pm2 start ecosystem.config.js

.PHONY: pm2-stop
pm2-stop: ## Stop PM2 services
	pm2 stop ecosystem.config.js

.PHONY: pm2-restart
pm2-restart: build ## Restart PM2 services
	pm2 restart ecosystem.config.js

.PHONY: pm2-reload
pm2-reload: build ## Reload PM2 services (zero downtime)
	pm2 reload ecosystem.config.js

.PHONY: pm2-delete
pm2-delete: ## Delete PM2 services
	pm2 delete ecosystem.config.js

.PHONY: pm2-logs
pm2-logs: ## Show PM2 logs
	pm2 logs

.PHONY: pm2-status
pm2-status: ## Show PM2 status
	pm2 status

.PHONY: pm2-monit
pm2-monit: ## Show PM2 monitoring
	pm2 monit

.PHONY: setup-logs
setup-logs: ## Create logs directory
	mkdir -p logs

# Utility commands
.PHONY: health
health: ## Check server health
	curl -f http://localhost:8080/health || echo "Server not running"

.PHONY: info
info: ## Show server info
	curl -s http://localhost:8080/ | jq . || echo "Server not running or jq not installed"

.PHONY: deps
deps: ## Download dependencies
	go mod download

.PHONY: deps-update
deps-update: ## Update dependencies
	go get -u ./...
	go mod tidy

# Git commands
.PHONY: git-init
git-init: ## Initialize git repository
	git init
	git add .
	git commit -m "Initial commit"

.PHONY: git-status
git-status: ## Show git status
	git status

# Environment setup
.PHONY: setup
setup: deps setup-logs ## Setup development environment
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from template"; fi
	@echo "Setup complete!"

.PHONY: setup-pm2
setup-pm2: setup build ## Setup PM2 environment
	./scripts/setup-pm2.sh

# All-in-one commands
.PHONY: install
install: setup ## Install and setup everything
	@echo "Installation complete!"

.PHONY: start
start: pm2-start ## Start all services
	@echo "All services started!"

.PHONY: stop
stop: pm2-stop ## Stop all services
	@echo "All services stopped!"

.PHONY: restart
restart: pm2-restart ## Restart all services
	@echo "All services restarted!"

# Check if required tools are installed
.PHONY: check-deps
check-deps: ## Check if required dependencies are installed
	@echo "Checking dependencies..."
	@command -v go >/dev/null 2>&1 || { echo "Go is not installed"; exit 1; }
	@command -v pm2 >/dev/null 2>&1 || { echo "PM2 is not installed. Run: npm install -g pm2"; exit 1; }
	@echo "All dependencies are installed!"
