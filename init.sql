-- Initialize database for MCP Server demo

-- Create sample tables
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(50),
    stock_quantity INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO users (name, email) VALUES 
    ('John Doe', '<EMAIL>'),
    ('Jane Smith', '<EMAIL>'),
    ('Bob Johnson', '<EMAIL>'),
    ('Alice Brown', '<EMAIL>'),
    ('Charlie Wilson', '<EMAIL>');

INSERT INTO products (name, description, price, category, stock_quantity) VALUES 
    ('Laptop Pro', 'High-performance laptop for professionals', 1299.99, 'Electronics', 50),
    ('Wireless Mouse', 'Ergonomic wireless mouse with long battery life', 29.99, 'Electronics', 200),
    ('Coffee Mug', 'Ceramic coffee mug with company logo', 12.99, 'Office', 100),
    ('Notebook', 'Premium leather-bound notebook', 24.99, 'Office', 75),
    ('Desk Lamp', 'LED desk lamp with adjustable brightness', 89.99, 'Office', 30);

INSERT INTO orders (user_id, total_amount, status) VALUES 
    (1, 1329.98, 'completed'),
    (2, 42.98, 'pending'),
    (3, 89.99, 'shipped'),
    (1, 24.99, 'completed'),
    (4, 1299.99, 'processing');

INSERT INTO order_items (order_id, product_id, quantity, unit_price) VALUES 
    (1, 1, 1, 1299.99),
    (1, 2, 1, 29.99),
    (2, 3, 1, 12.99),
    (2, 2, 1, 29.99),
    (3, 5, 1, 89.99),
    (4, 4, 1, 24.99),
    (5, 1, 1, 1299.99);

-- Create some useful views
CREATE VIEW user_orders AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email,
    o.id as order_id,
    o.total_amount,
    o.status,
    o.created_at as order_date
FROM users u
JOIN orders o ON u.id = o.user_id;

CREATE VIEW order_details AS
SELECT 
    o.id as order_id,
    u.name as customer_name,
    p.name as product_name,
    oi.quantity,
    oi.unit_price,
    (oi.quantity * oi.unit_price) as line_total,
    o.status,
    o.created_at as order_date
FROM orders o
JOIN users u ON o.user_id = u.id
JOIN order_items oi ON o.id = oi.order_id
JOIN products p ON oi.product_id = p.id;

CREATE VIEW product_sales AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.category,
    COUNT(oi.id) as times_ordered,
    SUM(oi.quantity) as total_quantity_sold,
    SUM(oi.quantity * oi.unit_price) as total_revenue
FROM products p
LEFT JOIN order_items oi ON p.id = oi.product_id
GROUP BY p.id, p.name, p.category;

-- Grant permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO mcpuser;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO mcpuser;
